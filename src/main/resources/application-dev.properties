spring.application.name=user-service
spring.liquibase.change-log=classpath:db.changelog/db.changelog-master.yaml
spring.liquibase.enabled=true

spring.config.import=optional:configserver:http://config-server:8888

spring.datasource.url=${USER_DB_URL:*********************************************}
spring.datasource.username=${USER_DB_USER:user_service}
spring.datasource.password=${USER_DB_PASSWORD:user_service}

spring.jpa.hibernate.ddl-auto=create-drop

logging.level.org.springframework.security=DEBUG

logging.level.org.springframework.web=DEBUG

logging.level.org.example.authenticationservice.config.security=DEBUG
logging.level.org.example.authenticationservice.service=DEBUG

logging.level.web=DEBUG

server.port=8088
