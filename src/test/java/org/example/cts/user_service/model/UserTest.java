package org.example.cts.user_service.model;

import static org.junit.jupiter.api.Assertions.*;

import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.example.cts.user_service.model.enums.OAuthProvider;
import org.example.cts.user_service.model.enums.Role;
import org.junit.jupiter.api.Test;

class UserTest {

    @Test
    void user_shouldCreateWithDefaultConstructor() {
        User user = new User();

        assertNotNull(user);
        assertNull(user.getId());
        assertNull(user.getUsername());
        assertNull(user.getPassword());
        assertNull(user.getCreatedAt());
        assertNull(user.getUpdatedAt());
        assertNotNull(user.getRoles());
        assertTrue(user.getRoles().isEmpty());
        assertNotNull(user.getOAuthProviders());
        assertTrue(user.getOAuthProviders().isEmpty());
    }

    @Test
    void user_shouldCreateWithAllArgsConstructor() {
        UUID id = UUID.randomUUID();
        String username = "<EMAIL>";
        String password = "password123";
        Instant createdAt = Instant.now();
        Instant updatedAt = Instant.now();
        Set<Role> roles = Set.of(Role.ROLE_USER);
        Set<OAuthProvider> oAuthProviders = Set.of(OAuthProvider.PROVIDER_GOOGLE);

        User user = new User(id, username, password, createdAt, updatedAt, roles, oAuthProviders);

        assertEquals(id, user.getId());
        assertEquals(username, user.getUsername());
        assertEquals(password, user.getPassword());
        assertEquals(createdAt, user.getCreatedAt());
        assertEquals(updatedAt, user.getUpdatedAt());
        assertEquals(roles, user.getRoles());
        assertEquals(oAuthProviders, user.getOAuthProviders());
    }

    @Test
    void user_shouldCreateWithBuilder() {
        UUID id = UUID.randomUUID();
        String username = "<EMAIL>";
        String password = "password123";
        Instant createdAt = Instant.now();
        Instant updatedAt = Instant.now();
        Set<Role> roles = Set.of(Role.ROLE_USER);
        Set<OAuthProvider> oAuthProviders = Set.of(OAuthProvider.PROVIDER_GOOGLE);

        User user = User.builder()
                .id(id)
                .username(username)
                .password(password)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .roles(roles)
                .oAuthProviders(oAuthProviders)
                .build();

        assertEquals(id, user.getId());
        assertEquals(username, user.getUsername());
        assertEquals(password, user.getPassword());
        assertEquals(createdAt, user.getCreatedAt());
        assertEquals(updatedAt, user.getUpdatedAt());
        assertEquals(roles, user.getRoles());
        assertEquals(oAuthProviders, user.getOAuthProviders());
    }

    @Test
    void user_shouldSetAndGetProperties() {
        User user = new User();
        UUID id = UUID.randomUUID();
        String username = "<EMAIL>";
        String password = "password123";
        Instant createdAt = Instant.now();
        Instant updatedAt = Instant.now();
        Set<Role> roles = new HashSet<>();
        roles.add(Role.ROLE_USER);
        Set<OAuthProvider> oAuthProviders = new HashSet<>();
        oAuthProviders.add(OAuthProvider.PROVIDER_GOOGLE);

        user.setId(id);
        user.setUsername(username);
        user.setPassword(password);
        user.setCreatedAt(createdAt);
        user.setUpdatedAt(updatedAt);
        user.setRoles(roles);
        user.setOAuthProviders(oAuthProviders);

        assertEquals(id, user.getId());
        assertEquals(username, user.getUsername());
        assertEquals(password, user.getPassword());
        assertEquals(createdAt, user.getCreatedAt());
        assertEquals(updatedAt, user.getUpdatedAt());
        assertEquals(roles, user.getRoles());
        assertEquals(oAuthProviders, user.getOAuthProviders());
    }

    @Test
    void user_shouldHandleNullRoles() {
        User user = new User();
        user.setRoles(null);

        // The setter initializes empty collection instead of setting null
        assertNotNull(user.getRoles());
        assertTrue(user.getRoles().isEmpty());
    }

    @Test
    void user_shouldHandleNullOAuthProviders() {
        User user = new User();
        user.setOAuthProviders(null);

        // The setter initializes empty collection instead of setting null
        assertNotNull(user.getOAuthProviders());
        assertTrue(user.getOAuthProviders().isEmpty());
    }

    @Test
    void user_builderShouldHaveDefaultValues() {
        User user = User.builder().build();

        assertNotNull(user);
        assertNull(user.getId());
        assertNull(user.getUsername());
        assertNull(user.getPassword());
        assertNull(user.getCreatedAt());
        assertNull(user.getUpdatedAt());
        assertNotNull(user.getRoles());
        assertTrue(user.getRoles().isEmpty());
        assertNotNull(user.getOAuthProviders());
        assertTrue(user.getOAuthProviders().isEmpty());
    }

    @Test
    void user_builderShouldChainMethods() {
        UUID id = UUID.randomUUID();
        String username = "<EMAIL>";

        User user = User.builder()
                .id(id)
                .username(username)
                .build();

        assertEquals(id, user.getId());
        assertEquals(username, user.getUsername());
        assertNotNull(user.getRoles());
        assertNotNull(user.getOAuthProviders());
    }
}
