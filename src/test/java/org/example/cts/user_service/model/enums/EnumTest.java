package org.example.cts.user_service.model.enums;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class EnumTest {

    @Test
    void role_shouldHaveCorrectValues() {
        Role[] roles = Role.values();

        assertEquals(1, roles.length);
        assertTrue(containsRole(roles, Role.ROLE_USER));
    }

    @Test
    void role_shouldConvertFromString() {
        assertEquals(Role.ROLE_USER, Role.valueOf("ROLE_USER"));
    }

    @Test
    void role_shouldThrowException_whenInvalidValue() {
        assertThrows(IllegalArgumentException.class, () -> Role.valueOf("INVALID_ROLE"));
    }

    @Test
    void role_shouldHaveCorrectToString() {
        assertEquals("ROLE_USER", Role.ROLE_USER.toString());
    }

    @Test
    void oAuthProvider_shouldHaveCorrectValues() {
        OAuthProvider[] providers = OAuthProvider.values();

        assertEquals(2, providers.length);
        assertTrue(containsProvider(providers, OAuthProvider.PROVIDER_GOOGLE));
        assertTrue(containsProvider(providers, OAuthProvider.PROVIDER_GITHUB));
    }

    @Test
    void oAuthProvider_shouldConvertFromString() {
        assertEquals(OAuthProvider.PROVIDER_GOOGLE, OAuthProvider.valueOf("PROVIDER_GOOGLE"));
        assertEquals(OAuthProvider.PROVIDER_GITHUB, OAuthProvider.valueOf("PROVIDER_GITHUB"));
    }

    @Test
    void oAuthProvider_shouldThrowException_whenInvalidValue() {
        assertThrows(IllegalArgumentException.class, () -> OAuthProvider.valueOf("INVALID_PROVIDER"));
    }

    @Test
    void oAuthProvider_shouldHaveCorrectToString() {
        assertEquals("PROVIDER_GOOGLE", OAuthProvider.PROVIDER_GOOGLE.toString());
        assertEquals("PROVIDER_GITHUB", OAuthProvider.PROVIDER_GITHUB.toString());
    }

    @Test
    void role_shouldBeComparable() {
        Role user = Role.ROLE_USER;

        assertEquals(user, Role.ROLE_USER);
    }

    @Test
    void oAuthProvider_shouldBeComparable() {
        OAuthProvider google = OAuthProvider.PROVIDER_GOOGLE;
        OAuthProvider github = OAuthProvider.PROVIDER_GITHUB;

        assertEquals(google, OAuthProvider.PROVIDER_GOOGLE);
        assertEquals(github, OAuthProvider.PROVIDER_GITHUB);
        assertNotEquals(google, github);
    }

    @Test
    void role_shouldHaveCorrectOrdinal() {
        assertEquals(0, Role.ROLE_USER.ordinal());
    }

    @Test
    void oAuthProvider_shouldHaveCorrectOrdinal() {
        assertEquals(0, OAuthProvider.PROVIDER_GOOGLE.ordinal());
        assertEquals(1, OAuthProvider.PROVIDER_GITHUB.ordinal());
    }

    private boolean containsRole(Role[] roles, Role target) {
        for (Role role : roles) {
            if (role == target) {
                return true;
            }
        }
        return false;
    }

    private boolean containsProvider(OAuthProvider[] providers, OAuthProvider target) {
        for (OAuthProvider provider : providers) {
            if (provider == target) {
                return true;
            }
        }
        return false;
    }
}
