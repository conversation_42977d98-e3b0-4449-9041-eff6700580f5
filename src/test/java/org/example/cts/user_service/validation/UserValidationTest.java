package org.example.cts.user_service.validation;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.example.cts.user_service.exception.UserNotFoundException;
import org.example.cts.user_service.model.User;
import org.example.cts.user_service.model.enums.Role;
import org.example.cts.user_service.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UserValidationTest {

    @Mock
    private UserRepository userRepository;

    private UserValidation userValidation;

    private final UUID userId = UUID.randomUUID();

    @BeforeEach
    void setUp() {
        userValidation = new UserValidation(userRepository);
    }

    @Test
    void validateUserExistence_shouldReturnUser_whenUserExists() {
        User expectedUser = createTestUser();
        when(userRepository.findById(userId)).thenReturn(Optional.of(expectedUser));

        User actualUser = userValidation.validateUserExistence(userId);

        assertNotNull(actualUser);
        assertEquals(expectedUser.getId(), actualUser.getId());
        assertEquals(expectedUser.getUsername(), actualUser.getUsername());
        assertEquals(expectedUser.getPassword(), actualUser.getPassword());
    }

    @Test
    void validateUserExistence_shouldThrowException_whenUserNotExists() {
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        UserNotFoundException exception = assertThrows(
                UserNotFoundException.class,
                () -> userValidation.validateUserExistence(userId)
        );

        assertEquals("User with id " + userId + " not found", exception.getMessage());
    }

    @Test
    void validateUserExistence_shouldThrowException_whenUserIdIsNull() {
        UUID nullId = null;
        when(userRepository.findById(nullId)).thenReturn(Optional.empty());

        UserNotFoundException exception = assertThrows(
                UserNotFoundException.class,
                () -> userValidation.validateUserExistence(nullId)
        );

        assertEquals("User with id null not found", exception.getMessage());
    }

    private User createTestUser() {
        User user = new User();
        user.setId(userId);
        user.setUsername("<EMAIL>");
        user.setPassword("hashedPassword");
        user.setRoles(Set.of(Role.ROLE_USER));
        user.setCreatedAt(Instant.now());
        user.setUpdatedAt(Instant.now());
        return user;
    }
}
