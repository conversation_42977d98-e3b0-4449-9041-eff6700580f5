package org.example.cts.user_service.validation;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import org.example.cts.user_service.exception.PasswordChangeNotAllowedException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

@ExtendWith(MockitoExtension.class)
class PasswordValidatorTest {

    @Mock
    private PasswordEncoder passwordEncoder;

    private PasswordValidator passwordValidator;

    @BeforeEach
    void setUp() {
        passwordValidator = new PasswordValidator(passwordEncoder);
    }

    @Test
    void validatePasswordNotNull_shouldPass_whenPasswordIsValid() {
        String validPassword = "validPassword";

        assertDoesNotThrow(() -> passwordValidator.validatePasswordNotNull(validPassword));
    }

    @Test
    void validatePasswordNotNull_shouldThrowException_whenPasswordIsNull() {
        PasswordChangeNotAllowedException exception = assertThrows(
                PasswordChangeNotAllowedException.class,
                () -> passwordValidator.validatePasswordNotNull(null)
        );

        assertEquals("Password change not allowed: no password is set for this account", exception.getMessage());
    }

    @Test
    void validatePasswordNotNull_shouldThrowException_whenPasswordIsBlank() {
        PasswordChangeNotAllowedException exception = assertThrows(
                PasswordChangeNotAllowedException.class,
                () -> passwordValidator.validatePasswordNotNull("   ")
        );

        assertEquals("Password change not allowed: no password is set for this account", exception.getMessage());
    }

    @Test
    void validatePasswordNotNull_shouldThrowException_whenPasswordIsEmpty() {
        PasswordChangeNotAllowedException exception = assertThrows(
                PasswordChangeNotAllowedException.class,
                () -> passwordValidator.validatePasswordNotNull("")
        );

        assertEquals("Password change not allowed: no password is set for this account", exception.getMessage());
    }

    @Test
    void validateOldPassword_shouldPass_whenPasswordMatches() {
        String oldPassword = "oldPassword";
        String currentPassword = "hashedCurrentPassword";

        when(passwordEncoder.matches(oldPassword, currentPassword)).thenReturn(true);

        assertDoesNotThrow(() -> passwordValidator.validateOldPassword(oldPassword, currentPassword));
    }

    @Test
    void validateOldPassword_shouldThrowException_whenPasswordDoesNotMatch() {
        String oldPassword = "wrongPassword";
        String currentPassword = "hashedCurrentPassword";

        when(passwordEncoder.matches(oldPassword, currentPassword)).thenReturn(false);

        PasswordChangeNotAllowedException exception = assertThrows(
                PasswordChangeNotAllowedException.class,
                () -> passwordValidator.validateOldPassword(oldPassword, currentPassword)
        );

        assertEquals("Old password is incorrect", exception.getMessage());
    }

    @Test
    void validateNewPassword_shouldPass_whenPasswordIsValidAndDifferent() {
        String newPassword = "newValidPassword123";
        String currentPassword = "hashedCurrentPassword";

        when(passwordEncoder.matches(newPassword, currentPassword)).thenReturn(false);

        assertDoesNotThrow(() -> passwordValidator.validateNewPassword(newPassword, currentPassword));
    }

    @Test
    void validateNewPassword_shouldThrowException_whenPasswordIsNull() {
        String currentPassword = "hashedCurrentPassword";

        PasswordChangeNotAllowedException exception = assertThrows(
                PasswordChangeNotAllowedException.class,
                () -> passwordValidator.validateNewPassword(null, currentPassword)
        );

        assertEquals("Password must be at least 6 characters long", exception.getMessage());
    }

    @Test
    void validateNewPassword_shouldThrowException_whenPasswordIsTooShort() {
        String newPassword = "12345"; // 5 characters, less than minimum 6
        String currentPassword = "hashedCurrentPassword";

        PasswordChangeNotAllowedException exception = assertThrows(
                PasswordChangeNotAllowedException.class,
                () -> passwordValidator.validateNewPassword(newPassword, currentPassword)
        );

        assertEquals("Password must be at least 6 characters long", exception.getMessage());
    }

    @Test
    void validateNewPassword_shouldThrowException_whenPasswordIsSameAsCurrent() {
        String newPassword = "samePassword";
        String currentPassword = "hashedCurrentPassword";

        when(passwordEncoder.matches(newPassword, currentPassword)).thenReturn(true);

        PasswordChangeNotAllowedException exception = assertThrows(
                PasswordChangeNotAllowedException.class,
                () -> passwordValidator.validateNewPassword(newPassword, currentPassword)
        );

        assertEquals("New password must be different from the old one", exception.getMessage());
    }

    @Test
    void validateNewPassword_shouldPass_whenPasswordIsExactlyMinimumLength() {
        String newPassword = "123456"; // exactly 6 characters
        String currentPassword = "hashedCurrentPassword";

        when(passwordEncoder.matches(newPassword, currentPassword)).thenReturn(false);

        assertDoesNotThrow(() -> passwordValidator.validateNewPassword(newPassword, currentPassword));
    }
}
