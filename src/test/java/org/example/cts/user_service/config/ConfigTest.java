package org.example.cts.user_service.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class ConfigTest {

    @Test
    void constants_shouldHaveCorrectBaseUrl() {
        assertEquals("/v1", Constants.BASE_URL);
    }

    @Test
    void constants_shouldCreateInstance() {
        Constants constants = new Constants();
        assertNotNull(constants);
    }

    @Test
    void validationConstants_shouldHaveCorrectMinPasswordLength() {
        assertEquals(6, ValidationConstants.MIN_PASSWORD_LENGTH);
    }

    @Test
    void validationConstants_shouldCreateInstance() {
        ValidationConstants validationConstants = new ValidationConstants();
        assertNotNull(validationConstants);
    }

    @Test
    void constants_baseUrlShouldNotBeNull() {
        assertNotNull(Constants.BASE_URL);
        assertFalse(Constants.BASE_URL.isEmpty());
    }

    @Test
    void validationConstants_minPasswordLengthShouldBePositive() {
        assertTrue(ValidationConstants.MIN_PASSWORD_LENGTH > 0);
    }

    @Test
    void validationConstants_minPasswordLengthShouldBeReasonable() {
        // Password length should be at least 6 characters for security
        assertTrue(ValidationConstants.MIN_PASSWORD_LENGTH >= 6);
        // But not too long to be impractical
        assertTrue(ValidationConstants.MIN_PASSWORD_LENGTH <= 20);
    }
}
