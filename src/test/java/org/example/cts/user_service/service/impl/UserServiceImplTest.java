package org.example.cts.user_service.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.example.cts.user_service.dto.ChangePasswordRequest;
import org.example.cts.user_service.model.User;
import org.example.cts.user_service.model.enums.Role;
import org.example.cts.user_service.repository.UserRepository;
import org.example.cts.user_service.validation.PasswordValidator;
import org.example.cts.user_service.validation.UserValidation;
import org.example.user.grpc.LoadUserByIdRequest;
import org.example.user.grpc.LoadUserByIdResponse;
import org.example.user.grpc.LoadUserByUsernameRequest;
import org.example.user.grpc.LoadUserByUsernameResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {

    @Mock
    private UserRepository userRepository;
    @Mock
    private PasswordValidator passwordValidator;
    @Mock
    private UserValidation userValidation;
    @Mock
    private PasswordEncoder passwordEncoder;
    @Mock
    private StreamObserver<LoadUserByUsernameResponse> usernameObserver;
    @Mock
    private StreamObserver<LoadUserByIdResponse> idObserver;

    @InjectMocks
    private UserServiceImpl userService;

    private final UUID userId = UUID.randomUUID();

    private User mockUser() {
        User user = new User();
        user.setId(userId);
        user.setUsername("<EMAIL>");
        user.setPassword("hashed-password");
        user.setRoles(Set.of(Role.ROLE_USER));
        user.setCreatedAt(Instant.now());
        user.setUpdatedAt(Instant.now());
        return user;
    }

    @Test
    void loadUserByUsername_shouldReturnUserDetails() {
        User user = mockUser();
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.of(user));

        LoadUserByUsernameRequest request = LoadUserByUsernameRequest.newBuilder().setUsername("<EMAIL>")
                .build();

        userService.loadUserByUsername(request, usernameObserver);

        verify(usernameObserver).onNext(any(LoadUserByUsernameResponse.class));
        verify(usernameObserver).onCompleted();
    }

    @Test
    void loadUserByUsername_shouldReturnNotFoundError() {
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.empty());

        LoadUserByUsernameRequest request = LoadUserByUsernameRequest.newBuilder()
                .setUsername("<EMAIL>")
                .build();

        userService.loadUserByUsername(request, usernameObserver);

        verify(usernameObserver).onError(any(StatusRuntimeException.class));
    }

    @Test
    void loadUserById_shouldReturnUserDetails() {
        User user = mockUser();
        when(userRepository.findById(userId)).thenReturn(Optional.of(user));

        LoadUserByIdRequest request = LoadUserByIdRequest.newBuilder().setUserId(userId.toString()).build();

        userService.loadUserById(request, idObserver);

        verify(idObserver).onNext(any(LoadUserByIdResponse.class));
        verify(idObserver).onCompleted();
    }

    @Test
    void loadUserById_shouldReturnNotFoundError() {
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        LoadUserByIdRequest request = LoadUserByIdRequest.newBuilder()
                .setUserId(userId.toString())
                .build();

        userService.loadUserById(request, idObserver);

        verify(idObserver).onError(any(StatusRuntimeException.class));
    }

    @Test
    void delete_shouldDeleteUser_whenUserExists() {
        User user = mockUser();
        when(userValidation.validateUserExistence(userId)).thenReturn(user);

        userService.delete(userId);

        verify(userRepository).delete(user);
    }

    @Test
    void changePassword_shouldUpdatePassword_whenValid() {
        User user = mockUser();
        ChangePasswordRequest request = new ChangePasswordRequest("old-pass", "new-pass");

        when(userValidation.validateUserExistence(userId)).thenReturn(user);
        when(passwordEncoder.encode("new-pass")).thenReturn("encoded-new");

        userService.changePassword(userId, request);

        verify(passwordValidator).validatePasswordNotNull(anyString());
        verify(passwordValidator).validateOldPassword(eq("old-pass"), anyString());
        verify(passwordValidator).validateNewPassword(eq("new-pass"), anyString());
        assertEquals("encoded-new", user.getPassword());
    }

    @Test
    void register_shouldThrowNotImplementedException() {
        RegisterRequestDto request = new RegisterRequestDto("<EMAIL>", "password", null);

        // Since register method is not implemented (just has TODO comment),
        // calling it should not throw any exception but also not do anything
        assertDoesNotThrow(() -> userService.register(request));
    }
}
