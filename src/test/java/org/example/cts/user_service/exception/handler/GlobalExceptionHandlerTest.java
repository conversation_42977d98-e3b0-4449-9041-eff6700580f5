package org.example.cts.user_service.exception.handler;

import static org.junit.jupiter.api.Assertions.*;

import org.example.cts.user_service.exception.PasswordChangeNotAllowedException;
import org.example.cts.user_service.exception.UserNotFoundException;
import org.example.cts.user_service.exception.UsernameAlreadyExistsException;
import org.cts.commonauth.exceptions.InvalidCookiesException;
import org.cts.commonauth.exceptions.GeneralError;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

class GlobalExceptionHandlerTest {

    private GlobalExceptionHandler globalExceptionHandler;

    @BeforeEach
    void setUp() {
        globalExceptionHandler = new GlobalExceptionHandler();
    }

    @Test
    void handleCustomerNotFoundException_shouldReturnNotFound() {
        String errorMessage = "User with id 123 not found";
        UserNotFoundException exception = new UserNotFoundException(errorMessage);

        ResponseEntity<GeneralError> response = globalExceptionHandler.handleCustomerNotFoundException(exception);

        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void handlePasswordChangeNotAllowedException_shouldReturnBadRequest() {
        String errorMessage = "Password change not allowed: old password is incorrect";
        PasswordChangeNotAllowedException exception = new PasswordChangeNotAllowedException(errorMessage);

        ResponseEntity<GeneralError> response = globalExceptionHandler.handlePasswordChangeNotAllowedException(exception);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void handleInvalidCookiesException_shouldReturnBadRequest() {
        String errorMessage = "Invalid cookies provided";
        InvalidCookiesException exception = new InvalidCookiesException(errorMessage);

        ResponseEntity<GeneralError> response = globalExceptionHandler.handleInvalidCookiesException(exception);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void handleUsernameExists_shouldReturnConflict() {
        String errorMessage = "Username already exists: <EMAIL>";
        UsernameAlreadyExistsException exception = new UsernameAlreadyExistsException(errorMessage);

        ResponseEntity<String> response = globalExceptionHandler.handleUsernameExists(exception);

        assertEquals(HttpStatus.CONFLICT, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(errorMessage, response.getBody());
    }

    @Test
    void handleUserNotFoundException_shouldHandleNullMessage() {
        UserNotFoundException exception = new UserNotFoundException(null);

        ResponseEntity<GeneralError> response = globalExceptionHandler.handleCustomerNotFoundException(exception);

        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void handlePasswordChangeNotAllowedException_shouldHandleEmptyMessage() {
        PasswordChangeNotAllowedException exception = new PasswordChangeNotAllowedException("");

        ResponseEntity<GeneralError> response = globalExceptionHandler.handlePasswordChangeNotAllowedException(exception);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
    }
}
