package org.example.cts.user_service.exception;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class ExceptionTest {

    @Test
    void userNotFoundException_shouldCreateWithMessage() {
        String message = "User not found";
        UserNotFoundException exception = new UserNotFoundException(message);

        assertEquals(message, exception.getMessage());
        assertNotNull(exception);
    }

    @Test
    void userNotFoundException_shouldCreateWithNullMessage() {
        UserNotFoundException exception = new UserNotFoundException(null);

        assertNull(exception.getMessage());
        assertNotNull(exception);
    }

    @Test
    void passwordChangeNotAllowedException_shouldCreateWithMessage() {
        String message = "Password change not allowed";
        PasswordChangeNotAllowedException exception = new PasswordChangeNotAllowedException(message);

        assertEquals(message, exception.getMessage());
        assertNotNull(exception);
    }

    @Test
    void passwordChangeNotAllowedException_shouldCreateWithNullMessage() {
        PasswordChangeNotAllowedException exception = new PasswordChangeNotAllowedException(null);

        assertNull(exception.getMessage());
        assertNotNull(exception);
    }

    @Test
    void usernameAlreadyExistsException_shouldCreateWithMessage() {
        String message = "Username already exists";
        UsernameAlreadyExistsException exception = new UsernameAlreadyExistsException(message);

        assertEquals(message, exception.getMessage());
        assertNotNull(exception);
    }

    @Test
    void usernameAlreadyExistsException_shouldCreateWithNullMessage() {
        UsernameAlreadyExistsException exception = new UsernameAlreadyExistsException(null);

        assertNull(exception.getMessage());
        assertNotNull(exception);
    }

    @Test
    void userAlreadyExistsException_shouldCreateWithMessage() {
        String message = "User already exists";
        UserAlreadyExistsException exception = new UserAlreadyExistsException(message);

        assertEquals(message, exception.getMessage());
        assertNotNull(exception);
    }

    @Test
    void userAlreadyExistsException_shouldCreateWithNullMessage() {
        UserAlreadyExistsException exception = new UserAlreadyExistsException(null);

        assertNull(exception.getMessage());
        assertNotNull(exception);
    }

    @Test
    void appMessage_shouldHaveCorrectConstants() {
        assertEquals("Username is required", AppMessage.ERR_USERNAME_REQUIRED);
        assertEquals("Either password or oAuthProvider must be provided", AppMessage.ERR_PASSWORD_OR_PROVIDER_REQUIRED);
        assertEquals("Unknown OAuth provider: ", AppMessage.ERR_UNKNOWN_OAUTH_PROVIDER);
        assertEquals("Username already in use: ", AppMessage.ERR_USERNAME_IN_USE);
        assertEquals("User already registered with provider: ", AppMessage.ERR_ALREADY_REGISTERED_WITH_PROVIDER);
    }

    @Test
    void appMessage_shouldCreateInstance() {
        AppMessage appMessage = new AppMessage();
        assertNotNull(appMessage);
    }
}
