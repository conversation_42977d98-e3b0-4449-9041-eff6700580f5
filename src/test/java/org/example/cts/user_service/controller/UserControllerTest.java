package org.example.cts.user_service.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.UUID;
import org.example.cts.user_service.dto.ChangePasswordRequest;
import org.example.cts.user_service.dto.RegisterRequestDto;
import org.example.cts.user_service.exception.UserNotFoundException;
import org.example.cts.user_service.exception.PasswordChangeNotAllowedException;
import org.example.cts.user_service.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

@WebMvcTest(UserController.class)
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private final UUID userId = UUID.randomUUID();

    @Test
    @WithMockUser
    void deleteUser_shouldReturnOk_whenUserExists() throws Exception {
        doNothing().when(userService).delete(userId);

        mockMvc.perform(delete("/v1/users/{id}", userId)
                .with(csrf()))
                .andExpect(status().isOk());

        verify(userService).delete(userId);
    }

    @Test
    @WithMockUser
    void deleteUser_shouldReturnNotFound_whenUserNotExists() throws Exception {
        doThrow(new UserNotFoundException("User not found")).when(userService).delete(userId);

        mockMvc.perform(delete("/v1/users/{id}", userId)
                .with(csrf()))
                .andExpect(status().isNotFound());

        verify(userService).delete(userId);
    }

    @Test
    @WithMockUser
    void changePassword_shouldReturnOk_whenValidRequest() throws Exception {
        ChangePasswordRequest request = new ChangePasswordRequest("oldPass", "newPass123");
        doNothing().when(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));

        mockMvc.perform(patch("/v1/users/{id}/password", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
                .andExpect(status().isOk());

        verify(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));
    }

    @Test
    @WithMockUser
    void changePassword_shouldReturnBadRequest_whenPasswordChangeNotAllowed() throws Exception {
        ChangePasswordRequest request = new ChangePasswordRequest("oldPass", "newPass123");
        doThrow(new PasswordChangeNotAllowedException("Password change not allowed"))
                .when(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));

        mockMvc.perform(patch("/v1/users/{id}/password", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));
    }

    @Test
    @WithMockUser
    void changePassword_shouldReturnNotFound_whenUserNotExists() throws Exception {
        ChangePasswordRequest request = new ChangePasswordRequest("oldPass", "newPass123");
        doThrow(new UserNotFoundException("User not found"))
                .when(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));

        mockMvc.perform(patch("/v1/users/{id}/password", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
                .andExpect(status().isNotFound());

        verify(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));
    }

    @Test
    @WithMockUser
    void register_shouldReturnCreated_whenValidRequest() throws Exception {
        RegisterRequestDto request = new RegisterRequestDto("<EMAIL>", "password123", null);
        doNothing().when(userService).register(any(RegisterRequestDto.class));

        mockMvc.perform(post("/v1/users/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
                .andExpect(status().isCreated());

        verify(userService).register(any(RegisterRequestDto.class));
    }

    @Test
    @WithMockUser
    void register_shouldReturnBadRequest_whenInvalidEmail() throws Exception {
        RegisterRequestDto request = new RegisterRequestDto("invalid-email", "password123", null);

        mockMvc.perform(post("/v1/users/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void register_shouldReturnBadRequest_whenBlankUsername() throws Exception {
        RegisterRequestDto request = new RegisterRequestDto("", "password123", null);

        mockMvc.perform(post("/v1/users/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
                .andExpect(status().isBadRequest());
    }
}
