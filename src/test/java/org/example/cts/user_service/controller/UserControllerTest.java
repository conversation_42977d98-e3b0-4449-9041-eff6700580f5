package org.example.cts.user_service.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.UUID;
import org.example.cts.user_service.dto.ChangePasswordRequest;
import org.example.cts.user_service.dto.RegisterRequestDto;
import org.example.cts.user_service.exception.UserNotFoundException;
import org.example.cts.user_service.exception.PasswordChangeNotAllowedException;
import org.example.cts.user_service.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

@WebMvcTest(UserController.class)
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private final UUID userId = UUID.randomUUID();

    @Test
    void deleteUser_shouldReturnOk_whenUserExists() throws Exception {
        doNothing().when(userService).delete(userId);

        mockMvc.perform(delete("/api/v1/users/{id}", userId))
                .andExpect(status().isOk());

        verify(userService).delete(userId);
    }

    @Test
    void deleteUser_shouldReturnNotFound_whenUserNotExists() throws Exception {
        doThrow(new UserNotFoundException("User not found")).when(userService).delete(userId);

        mockMvc.perform(delete("/api/v1/users/{id}", userId))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.error").value("User not found"))
                .andExpect(jsonPath("$.message").value("User not found"));

        verify(userService).delete(userId);
    }

    @Test
    void changePassword_shouldReturnOk_whenValidRequest() throws Exception {
        ChangePasswordRequest request = new ChangePasswordRequest("oldPass", "newPass123");
        doNothing().when(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));

        mockMvc.perform(patch("/api/v1/users/{id}/password", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        verify(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));
    }

    @Test
    void changePassword_shouldReturnBadRequest_whenPasswordChangeNotAllowed() throws Exception {
        ChangePasswordRequest request = new ChangePasswordRequest("oldPass", "newPass123");
        doThrow(new PasswordChangeNotAllowedException("Password change not allowed"))
                .when(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));

        mockMvc.perform(patch("/api/v1/users/{id}/password", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("Password change not allowed"))
                .andExpect(jsonPath("$.message").value("Password change not allowed"));

        verify(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));
    }

    @Test
    void changePassword_shouldReturnNotFound_whenUserNotExists() throws Exception {
        ChangePasswordRequest request = new ChangePasswordRequest("oldPass", "newPass123");
        doThrow(new UserNotFoundException("User not found"))
                .when(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));

        mockMvc.perform(patch("/api/v1/users/{id}/password", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpected(status().isNotFound())
                .andExpect(jsonPath("$.error").value("User not found"))
                .andExpect(jsonPath("$.message").value("User not found"));

        verify(userService).changePassword(eq(userId), any(ChangePasswordRequest.class));
    }

    @Test
    void register_shouldReturnCreated_whenValidRequest() throws Exception {
        RegisterRequestDto request = new RegisterRequestDto("<EMAIL>", "password123", null);
        doNothing().when(userService).register(any(RegisterRequestDto.class));

        mockMvc.perform(post("/api/v1/users/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());

        verify(userService).register(any(RegisterRequestDto.class));
    }

    @Test
    void register_shouldReturnBadRequest_whenInvalidEmail() throws Exception {
        RegisterRequestDto request = new RegisterRequestDto("invalid-email", "password123", null);

        mockMvc.perform(post("/api/v1/users/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void register_shouldReturnBadRequest_whenBlankUsername() throws Exception {
        RegisterRequestDto request = new RegisterRequestDto("", "password123", null);

        mockMvc.perform(post("/api/v1/users/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
}
